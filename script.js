// Enhanced Mouse Follower with Modern Effects
function mousefollow() {
    const cursor = document.querySelector("#mousefollower");
    const cursorDot = document.querySelector(".cursor-dot");
    const cursorOutline = document.querySelector(".cursor-outline");

    let mouseX = 0, mouseY = 0;
    let outlineX = 0, outlineY = 0;

    document.addEventListener("mousemove", function(e) {
        mouseX = e.clientX;
        mouseY = e.clientY;

        cursorDot.style.left = mouseX + "px";
        cursorDot.style.top = mouseY + "px";
    });

    // Smooth follow for outline
    function animateOutline() {
        outlineX += (mouseX - outlineX) * 0.1;
        outlineY += (mouseY - outlineY) * 0.1;

        cursorOutline.style.left = outlineX + "px";
        cursorOutline.style.top = outlineY + "px";

        requestAnimationFrame(animateOutline);
    }
    animateOutline();

    // Add hover effects for interactive elements
    const interactiveElements = document.querySelectorAll('a, button, .magnetic-btn, .service-card');

    interactiveElements.forEach(el => {
        el.addEventListener('mouseenter', () => {
            cursor.classList.add('cursor-hover');
        });

        el.addEventListener('mouseleave', () => {
            cursor.classList.remove('cursor-hover');
        });

        el.addEventListener('mousedown', () => {
            cursor.classList.add('cursor-click');
        });

        el.addEventListener('mouseup', () => {
            cursor.classList.remove('cursor-click');
        });
    });
}

// Particles.js Configuration
function initParticles() {
    particlesJS('particles-js', {
        particles: {
            number: {
                value: 80,
                density: {
                    enable: true,
                    value_area: 800
                }
            },
            color: {
                value: "#00f5ff"
            },
            shape: {
                type: "circle",
                stroke: {
                    width: 0,
                    color: "#000000"
                }
            },
            opacity: {
                value: 0.5,
                random: false,
                anim: {
                    enable: false,
                    speed: 1,
                    opacity_min: 0.1,
                    sync: false
                }
            },
            size: {
                value: 3,
                random: true,
                anim: {
                    enable: false,
                    speed: 40,
                    size_min: 0.1,
                    sync: false
                }
            },
            line_linked: {
                enable: true,
                distance: 150,
                color: "#00f5ff",
                opacity: 0.4,
                width: 1
            },
            move: {
                enable: true,
                speed: 6,
                direction: "none",
                random: false,
                straight: false,
                out_mode: "out",
                bounce: false,
                attract: {
                    enable: false,
                    rotateX: 600,
                    rotateY: 1200
                }
            }
        },
        interactivity: {
            detect_on: "canvas",
            events: {
                onhover: {
                    enable: true,
                    mode: "repulse"
                },
                onclick: {
                    enable: true,
                    mode: "push"
                },
                resize: true
            },
            modes: {
                grab: {
                    distance: 400,
                    line_linked: {
                        opacity: 1
                    }
                },
                bubble: {
                    distance: 400,
                    size: 40,
                    duration: 2,
                    opacity: 8,
                    speed: 3
                },
                repulse: {
                    distance: 200,
                    duration: 0.4
                },
                push: {
                    particles_nb: 4
                },
                remove: {
                    particles_nb: 2
                }
            }
        },
        retina_detect: true
    });
}

// Scroll Progress Indicator
function initScrollProgress() {
    const progressBar = document.querySelector('.progress-bar');

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrollProgress = (scrollTop / scrollHeight) * 100;

        progressBar.style.width = scrollProgress + '%';
    });
}

// Typing Animation
function initTypingAnimation() {
    const typingElement = document.querySelector('.typing-text');
    const text = typingElement.getAttribute('data-text');

    let i = 0;
    typingElement.textContent = '';

    function typeWriter() {
        if (i < text.length) {
            typingElement.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        }
    }

    // Start typing after page load
    setTimeout(typeWriter, 2000);
}

// Magnetic Button Effect
function initMagneticButtons() {
    const magneticButtons = document.querySelectorAll('.magnetic-btn');

    magneticButtons.forEach(btn => {
        btn.addEventListener('mousemove', (e) => {
            const rect = btn.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            btn.style.transform = `translate(${x * 0.3}px, ${y * 0.3}px)`;
        });

        btn.addEventListener('mouseleave', () => {
            btn.style.transform = 'translate(0px, 0px)';
        });
    });
}

// Enhanced Navigation Scroll Effect
function initNavScrollEffect() {
    const nav = document.querySelector('#nav');

    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
    });
}

// Tilt Effect for Service Cards
function initTiltEffect() {
    VanillaTilt.init(document.querySelectorAll("[data-tilt]"), {
        max: 15,
        speed: 400,
        glare: true,
        "max-glare": 0.2,
    });
}

// Floating Icons Animation
function initFloatingIcons() {
    const floatingIcons = document.querySelectorAll('.floating-icon');

    floatingIcons.forEach((icon) => {
        // Add random delay and duration for more natural movement
        const delay = Math.random() * 2;
        const duration = 4 + Math.random() * 4;

        icon.style.animationDelay = delay + 's';
        icon.style.animationDuration = duration + 's';

        // Add click interaction
        icon.addEventListener('click', () => {
            icon.style.transform = 'scale(1.5) rotate(360deg)';
            setTimeout(() => {
                icon.style.transform = '';
            }, 600);
        });
    });
}
// Global variable to store locomotive scroll instance
let locoScrollInstance = null;

function loco(){
    gsap.registerPlugin(ScrollTrigger);

    // Using Locomotive Scroll from Locomotive https://github.com/locomotivemtl/locomotive-scroll

    locoScrollInstance = new LocomotiveScroll({
      el: document.querySelector("#main"),
      smooth: true
    });

    // each time Locomotive Scroll updates, tell ScrollTrigger to update too (sync positioning)
    locoScrollInstance.on("scroll", ScrollTrigger.update);

    // tell ScrollTrigger to use these proxy methods for the "#main" element since Locomotive Scroll is hijacking things
    ScrollTrigger.scrollerProxy("#main", {
      scrollTop(value) {
        return arguments.length ? locoScrollInstance.scrollTo(value, 0, 0) : locoScrollInstance.scroll.instance.scroll.y;
      }, // we don't have to define a scrollLeft because we're only scrolling vertically.
      getBoundingClientRect() {
        return {top: 0, left: 0, width: window.innerWidth, height: window.innerHeight};
      },
      // LocomotiveScroll handles things completely differently on mobile devices - it doesn't even transform the container at all! So to get the correct behavior and avoid jitters, we should pin things with position: fixed on mobile. We sense it by checking to see if there's a transform applied to the container (the LocomotiveScroll-controlled element).
      pinType: document.querySelector("#main").style.transform ? "transform" : "fixed"
    });

    // each time the window updates, we should refresh ScrollTrigger and then update LocomotiveScroll.
    ScrollTrigger.addEventListener("refresh", () => locoScrollInstance.update());

    // after everything is set up, refresh() ScrollTrigger and update LocomotiveScroll because padding may have been added for pinning, etc.
    ScrollTrigger.refresh();
}

function time(){
    var a = 0;
    setInterval(function(){
        a = a + (Math.floor(Math.random()*15));
        if(a<100){
            document.querySelector("#loader h1").innerHTML = a +"%"
        }
        else{
            a=100;
            document.querySelector("#loader h1").innerHTML = a +"%"
        }
    },150)
}

function homeanimation(){
    gsap.from("#homeleft",{
        opacity:0,
        delay:6,
        ease: Power2
    })
    var tl = gsap.timeline();
    
        tl
        .to("#loader h1",{
            scale: 2  ,
            duration: 2,
            onStart: time()
        })
        .to("#loader",{
            top: "-100vh",
            delay: 0.5,
            duration: 2,
        })
        .from("#nav",{
            opacity: 0,
            y: -50,
        })
            .from("#navleft a",{
                y:-100,
                stagger: .4
            })
            .from("#homeright h2",{
                opacity: 0,
                x: 40, 
                ease: Expo.easeInOut
            })
            .from("#homeright h1",{
                opacity: 0,
                ease: Expo.easeInOut
            })
            .from("#homeright p",{
                opacity: 0,
                stagger:0.2,
                ease: Expo.easeInOut
            })
            .from("#homeright i",{
                opacity: 0,
                stagger:0.2,
                ease: Expo.easeInOut
            })
            .from("#homeright button",{
                opacity: 0,
                ease: Expo.easeInOut
            })
}
function aboutanim(){
    gsap.from("#aboutright",{
        opacity: 0,
        scale: 0,
        scrollTrigger: {
            trigger: "#aboutright",
            scroller: "#main",
            // markers: true,
            scrub: true,
            start: "top 90%",
            end: "top 50%"
        }
    })
    gsap.from("#aboutleft h1,#aboutleft h2 ,#aboutleft p",{
        opacity: 0,
        y: 50,
        stagger: .4,
        scrollTrigger: {
            trigger: "#aboutleft h1,#aboutleft h2 ,#aboutleft p",
            scroller: "#main",
            // markers: true,
            scrub: true,
            start: "top 90%",
            end: "top 50%"
        }
    }) 
    gsap.from("#aboutleft button",{
        opacity: 0,
        scale: 0,
        scrollTrigger: {
            trigger: "#aboutleft button",
            scroller: "#main",
            // markers: true,
            scrub: true,
            start: "top 100%",
            end: "top 80%"
        }
    })   
}    
function myservice(){
    gsap.from("#services h1",{
        opacity: 0,
        scale: 0,
        duration: 2,
        scrollTrigger: {
            trigger: "#services h1",
            scroller: "#main",
            // markers: true,
            end: "top 50%",
            scrub: true
        }
    })
    gsap.from("#boxes i, #boxes h3, #boxes p, #boxes button",{
        opacity: 0,
        scale: 0,
        scrollTrigger: {
            trigger: "#boxes i, #boxes h3, #boxes p, #boxes button",
            scroller: "#main",
            // markers: true,
            start: "top 80%",
            end: "top 50%",
            scrub: true
        }
    })
}
function myskill() {
    gsap.from("#skills h1",{
        y: -150,
        opacity: 0,
        ease: Power1,
        scrollTrigger: {
            trigger: "#skillright h1",
            scroller: "#main",
            // markers: true,
            end: "top 60%",
            scrub: true
        } 
    })
    gsap.from("#skillright .anim",{
        width: 0,
        ease: Power1,
        duration: 1.5,
        scrollTrigger: {
            trigger: "#skillright .anim",
            scroller: "#main",
            // markers: true,
            end: "top 60%",
            scrub: true
        } 
    })
    gsap.from("#contain #creativity, #contain #communication, #contain #problem, #contain #team",{
        opacity: 0,
        stagger: 1,
        ease: Power1,
        scrollTrigger: {
            trigger: "#contain #creativity, #contain #communication, #contain #problem, #contain #team",
            scroller: "#main",
            // markers: true,
            start: "top 80%",
            end: "top 50%",
            scrub: true
        } 
    })
}
function project() {
    gsap.from("#project h1",{
        y:-50,
        opacity: 0,
        stagger: .5,
        ease: Power1,
        scrollTrigger: {
            trigger: "#project h1",
            scroller: "#main",
            // markers: true,
            start: "top 80%",
            end: "top 60%",
            scrub: true
        }
    })
    gsap.from(".item a img",{
        scale: 0,
        ease: Power1,
        scrollTrigger: {
            trigger: ".item img",
            scroller: "#main",
            // markers: true,
            start: "top 105%",
            end: "top 60%",
            scrub: true
        }
    })
    gsap.from(".item h2",{
        y: 100,
        ease: Power1,
        scrollTrigger: {
            trigger: ".item img",
            scroller: "#main",
            end: "top 60%",
            scrub: true
        }
    })
}
function contact() {
    gsap.from("#lcontact h1",{
        scale: 0,
        y:-50,
        opacity: 0,
        ease: Power1,
        scrollTrigger: {
            trigger: "#lcontact h1",
            scroller: "#main",
            // markers: true,
            end: "top 60%",
            scrub: true
        }
    })
    gsap.from("#lcontact h3, #lcontact p, #lcontact i",{
        x:-50,
        opacity: 0,
        stagger: .5,
        ease: Power1,
        scrollTrigger: {
            trigger: "#lcontact h3, #lcontact p, #lcontact i",
            scroller: "#main",
            // markers: true,
            start: "top 80%",
            end: "top 43%",
            scrub: true
        }
    })
    gsap.from("form input, textarea",{
        x:50,
        opacity: 0,
        stagger: .5,
        ease: Power1,
        scrollTrigger: {
            trigger: "form input, textarea",
            scroller: "#main",
            // markers: true,
            start: "top 100%",
            end: "top 70%",
            scrub: true
        }
    })
    gsap.from("form button",{
        scale: 0,
        opacity: 0,
        ease: Power1,
        scrollTrigger: {
            trigger: "form button",
            scroller: "#main",
            // markers: true,
            start: "top 100%",
            end: "top 92%",
            scrub: true
        }
    })
}
function navpopup(){
    var menu = document.querySelector("#menu")
    var close = document.querySelector("#close")
    
    menu.addEventListener("click",function(){
        var tl = gsap.timeline();
       tl.to("#nav-PopUp",{
        top: 0,
        duration: 0.5,
        ease: Power1
       })
       tl.from("#links a",{
        opacity: 0,
        y: -100,
        stagger: 0.5,
        ease: Power1
       })
    })
    close.addEventListener("click",function(){
        gsap.to("#nav-PopUp",{
            top: "-100%",
            duration: 0.5,
            ease: Power1
           })
    })
}
// Enhanced Contact Form
function initContactForm() {
    const form = document.getElementById('contactForm');
    const formStatus = document.getElementById('formStatus');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(form);
            const name = formData.get('name');
            const email = formData.get('email');
            const subject = formData.get('subject');
            const message = formData.get('message');

            // Simple validation
            if (!name || !email || !subject || !message) {
                showFormStatus('Please fill in all fields.', 'error');
                return;
            }

            if (!isValidEmail(email)) {
                showFormStatus('Please enter a valid email address.', 'error');
                return;
            }

            // Simulate form submission
            showFormStatus('Sending message...', 'success');

            setTimeout(() => {
                showFormStatus('Thank you! Your message has been sent successfully.', 'success');
                form.reset();

                // Hide status after 5 seconds
                setTimeout(() => {
                    formStatus.style.opacity = '0';
                }, 5000);
            }, 2000);
        });
    }
}

function showFormStatus(message, type) {
    const formStatus = document.getElementById('formStatus');
    formStatus.textContent = message;
    formStatus.className = `form-status ${type}`;
    formStatus.style.opacity = '1';
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Smooth Scroll for Navigation Links
function initSmoothScroll() {
    const navLinks = document.querySelectorAll('#navleft a, #links a, .footer-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Only prevent default for internal links (starting with #)
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const targetElement = document.querySelector(href);

                if (targetElement) {
                    // Close mobile menu if open
                    const navPopup = document.querySelector('#nav-PopUp');
                    if (navPopup) {
                        navPopup.style.top = '-100%';
                    }

                    // Use locomotive scroll if available, otherwise use regular scroll
                    if (locoScrollInstance) {
                        locoScrollInstance.scrollTo(targetElement);
                    } else {
                        const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                }
            }
        });
    });
}

// Back to Top Button
function initBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');

    window.addEventListener('scroll', () => {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });

    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Performance optimization - Intersection Observer for animations
function initIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .floating-icon, .social-link');
    animateElements.forEach(el => observer.observe(el));
}

// Preload critical resources
function preloadResources() {
    const criticalImages = [
        './mine.webp'
    ];

    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Initialize all functions
document.addEventListener('DOMContentLoaded', function() {
    // Preload resources first
    preloadResources();

    // Initialize modern features
    initParticles();
    initScrollProgress();
    initTypingAnimation();
    initMagneticButtons();
    initNavScrollEffect();
    initFloatingIcons();
    initContactForm();
    initSmoothScroll();
    initBackToTop();
    initIntersectionObserver();

    // Initialize existing functions
    navpopup();
    loco();
    mousefollow();
    myskill();
    homeanimation();
    aboutanim();
    myservice();
    project();
    contact();

    // Initialize tilt effect after DOM is loaded
    setTimeout(() => {
        initTiltEffect();
    }, 1000);

    // Add loading complete class
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 3000);
});