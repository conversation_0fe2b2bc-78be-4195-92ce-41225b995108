@font-face {
    font-family: Averta;
    src: url(./AvertaCY-Light.woff2);
}

/* CSS Custom Properties for Modern Design */
:root {
    --primary-color: #00f5ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-muted: #6c757d;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.3);
    --border-radius: 16px;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', 'Averta', sans-serif;
}

html {
    scroll-behavior: smooth;
}

html,
body {
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    overflow-x: hidden;
    cursor: none;
}

::selection {
    background: var(--primary-color);
    color: var(--bg-primary);
}

/* Particles Background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    opacity: 0.6;
}

/* Scroll Progress Indicator */
#scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    z-index: 1000;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-accent);
    width: 0%;
    transition: width 0.3s ease;
}
/* Enhanced Loader */
#loader {
    width: 100vw;
    height: 100vh;
    position: fixed;
    background: var(--bg-primary);
    background-image: var(--gradient-primary);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.loader-content {
    text-align: center;
    position: relative;
}

.loader-circle {
    width: 120px;
    height: 120px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top: 3px solid var(--primary-color);
    margin: 0 auto 2rem;
    animation: spin 1s linear infinite;
    position: relative;
}

.loader-inner {
    width: 80px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-bottom: 2px solid var(--accent-color);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: spin 0.8s linear infinite reverse;
}

.loader-text {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loader-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Mouse Follower */
#mousefollower {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    pointer-events: none;
    mix-blend-mode: difference;
}

.cursor-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    transition: all 0.1s ease;
    box-shadow: 0 0 20px var(--primary-color);
}

.cursor-outline {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(0, 245, 255, 0.5);
    border-radius: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    transition: all 0.15s ease;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 245, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 245, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 245, 255, 0);
    }
}

.cursor-hover .cursor-outline {
    width: 60px;
    height: 60px;
    border-color: var(--secondary-color);
}

.cursor-click .cursor-dot {
    transform: translate(-50%, -50%) scale(1.5);
    background: var(--secondary-color);
}

#main {
    max-width: 100vw;
    color: white;
    position: relative;
}

/* Mobile Navigation Popup */
#nav-PopUp{
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    z-index: 9999;
    transition: var(--transition-smooth);
    padding-top: 100px;
}

#links {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 2rem;
}

#links a {
    font-size: 2rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-smooth);
}

#links a:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

#close {
    position: absolute;
    top: 2rem;
    right: 2rem;
    font-size: 2rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-smooth);
}

#close:hover {
    color: var(--primary-color);
    transform: rotate(90deg);
}
/* Enhanced Navigation */
#nav {
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    padding: 1.5rem 5vw;
    font-size: 1rem;
    background: rgba(26, 26, 46, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition-smooth);
}

#nav.scrolled {
    padding: 1rem 5vw;
    background: rgba(10, 10, 15, 0.95);
    box-shadow: var(--shadow-light);
}

#navright a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
}

#navright h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
}

#navleft {
    display: flex;
    align-items: center;
    gap: 2rem;
}

#navleft a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-smooth);
    position: relative;
}

#navleft a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition-smooth);
}

#navleft a:hover {
    color: var(--primary-color);
}

#navleft a:hover::after {
    width: 100%;
}

#navleft i {
    font-size: 1.5rem;
    color: var(--primary-color);
    display: none;
    cursor: pointer;
}

#navright {
    display: block;
    font-size: 1.6vw;
    color: white;
}
#navright a{
    text-decoration: none;
    color: white;
}
#navleft {
    display: flex;
}

#navleft a {
    text-decoration: none;
    color: white;
    display: block;
    padding-left: 2vw;
    font-weight: 600;
}

#navleft i {
    font-size: 1.6vw;
    color: cyan;
    display: none;
}

#navleft a:hover {
    filter: drop-shadow(0px 0px 2px aqua);
}

#home {
    width: 100vw;
    min-height: 100vh;
    display: flex;
    position: relative;
    padding-top: 80px; /* Account for fixed navbar */
}

/* Enhanced Home Section */
#homeright {
    width: 50%;
    padding: 18vh 5vw;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-content {
    max-width: 600px;
}

.greeting {
    font-size: 1.5rem;
    font-weight: 300;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
    animation-delay: 0.5s;
}

.name-title {
    font-size: 4rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
    line-height: 1.1;
}

.typing-text {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cursor-blink {
    animation: blink 1s infinite;
    color: var(--primary-color);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.role-title {
    font-size: 2rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.highlight-text {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.hero-description {
    font-size: 1.1rem;
    font-weight: 400;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
    animation-delay: 1s;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Social Icons */
.social-icons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.social-link {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-smooth);
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-accent);
    transition: var(--transition-smooth);
    z-index: -1;
}

.social-link:hover::before {
    left: 0;
}

.social-link:hover {
    color: var(--bg-primary);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 245, 255, 0.3);
}

.social-link i {
    font-size: 1.2rem;
    z-index: 1;
}

/* Tooltip for social links */
.social-link::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.8rem;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition-smooth);
    white-space: nowrap;
}

.social-link:hover::after {
    opacity: 1;
    bottom: -35px;
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--gradient-accent);
    color: var(--bg-primary);
    box-shadow: 0 8px 25px rgba(0, 245, 255, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--primary-color);
}

.btn-primary:hover,
.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 245, 255, 0.4);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
}

/* Magnetic Button Effect */
.magnetic-btn {
    transition: var(--transition-smooth);
}

.magnetic-btn:hover {
    transform: scale(1.05);
}

/* Enhanced Image Section */
#homeleft {
    width: 50%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.image-container {
    position: relative;
    width: 400px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-image {
    width: 350px;
    height: 350px;
    border-radius: 50%;
    object-fit: cover;
    object-position: top;
    border: 4px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-smooth);
    position: relative;
    z-index: 2;
}

.profile-image:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.image-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 380px;
    height: 380px;
    background: var(--gradient-accent);
    border-radius: 50%;
    opacity: 0.3;
    filter: blur(20px);
    animation: glow 3s ease-in-out infinite alternate;
    z-index: 1;
}

@keyframes glow {
    from {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* Floating Tech Icons */
.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    animation: float 6s ease-in-out infinite;
    transition: var(--transition-smooth);
}

.floating-icon:hover {
    transform: scale(1.2);
    background: var(--primary-color);
    color: var(--bg-primary);
}

.floating-icon[data-icon="html"] {
    top: 10%;
    right: 10%;
    animation-delay: 0s;
    color: #e34c26;
}

.floating-icon[data-icon="css"] {
    top: 60%;
    right: 5%;
    animation-delay: 1.5s;
    color: #1572b6;
}

.floating-icon[data-icon="js"] {
    bottom: 20%;
    left: 5%;
    animation-delay: 3s;
    color: #f7df1e;
}

.floating-icon[data-icon="react"] {
    top: 20%;
    left: 10%;
    animation-delay: 4.5s;
    color: #61dafb;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

#aboutme {
    width: 100vw;
    height: 100vh;
    display: flex;
}

#aboutright {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

#aboutright img {
    width: 60%;
    height: 70%;
    padding: 0.6vw;
    border: 5px solid white;
    border-radius: 50%;
    object-fit: cover;
    object-position: top;
    transition: all ease 1s;
    box-shadow: 0px 0px 6vw aqua;
}

#aboutright img:hover {
    scale: 1.1;
}

#aboutleft {
    width: 50%;
    height: 100vh;
    /* border: 2px solid blue; */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

#aboutleft h1 {
    font-size: 4vw;
    font-weight: 400;
}

#aboutleft h2 {
    font-size: 2vw;
    letter-spacing: 1vh;
    margin-bottom: 2vh;
}

#aboutleft h2:hover {
    color: transparent;
    -webkit-text-stroke: 1px white;
}

#aboutleft p {
    font-size: 1vw;
}

#aboutleft button {
    margin-top: 5vh;
}

/* Enhanced Services Section */
#services {
    width: 100%;
    min-height: 100vh;
    padding: 8vh 5vw;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 5rem;
}

.section-header h1 {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 300;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 2rem;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-accent);
    opacity: 0.1;
    transition: var(--transition-smooth);
    z-index: 0;
}

.service-card:hover::before {
    left: 0;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.card-header {
    position: relative;
    z-index: 1;
    margin-bottom: 1.5rem;
}

.icon-wrapper {
    width: 70px;
    height: 70px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    transition: var(--transition-smooth);
}

.service-card:hover .icon-wrapper {
    transform: scale(1.1) rotate(5deg);
}

.icon-wrapper i {
    font-size: 2rem;
    color: var(--bg-primary);
}

.card-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.card-content {
    position: relative;
    z-index: 1;
    flex-grow: 1;
    margin-bottom: 1.5rem;
}

.card-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    padding: 0;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.service-features i {
    color: var(--primary-color);
    font-size: 1rem;
}

.card-footer {
    position: relative;
    z-index: 1;
}

.service-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: transparent;
    border: 2px solid var(--primary-color);
    border-radius: 50px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition-smooth);
}

.service-btn:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
    transform: translateX(5px);
}

#skills {
    width: 100vw;
    min-height: 100vh;
    padding: 6vh 3vw;
}

#skills>h1 {
    font-size: 3.5vw;
    text-align: center;
}

#skillchart {
    width: 100%;
    margin-top: 4vh;
    display: flex;
    justify-content: space-around;
}

.skill h1 {
    font-size: 2vw;
    text-align: center;
    margin-bottom: 4vh;
}

#skillright {
    width: 100%;
    /* border: 2px solid green; */
}

#skillchart h1:hover {
    text-decoration: underline;
    color: transparent;
    -webkit-text-stroke: 1px white;
    filter: drop-shadow(0px 0px 5px cyan);
}

.percentage {
    display: flex;
    justify-content: space-between;
    align-items: end;
    width: 80%;
    font-size: 1.2vw;
}

#skillright i {
    font-size: 2vw;
}

#skillright h3 {
    font-size: 1.23vw;
    /* margin: 1vh 0vw; */
    font-weight: 400;
}

#skillright h3:hover {
    filter: drop-shadow(0px 0px 10px cyan);
}

.bar {
    width: 80%;
    height: 1vh;
    border-radius: 5px;
    background-color: rgb(0, 0, 0);
    margin-bottom: 3vh;
}

.bar:hover {
    filter: drop-shadow(0px 0px 10px cyan);
}

#hpercent {
    width: 90%;
    height: 100%;
    background-color: aqua;
    border-radius: 5px;
}

#cpercent {
    width: 85%;
    height: 100%;
    background-color: aqua;
    border-radius: 5px;
}

#css i {
    color: lightblue;
}

#html i {
    color: red;
}

#jspercent {
    width: 85%;
    height: 100%;
    background-color: aqua;
    border-radius: 5px;
}

#js i {
    color: yellowgreen;
}

#reactpercent {
    width: 75%;
    height: 100%;
    background-color: aqua;
    border-radius: 5px;
}

#react i {
    color: cyan;
}

#skillleft {
    width: 100%;
    padding: 0vh 2vw;
}

#contain {
    width: 100%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

#creativity {
    width: 40%;
    height: 40%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

#creativity .progress {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40%;
    height: 70%;
    border-radius: 50%;
    background:
        radial-gradient(closest-side, rgb(30, 30, 43) 90%, transparent 80% 100%),
        conic-gradient(cyan 80%, rgb(0, 0, 0) 0);
}

#creativity .progress::before {
    content: "80%";
    font-size: 1.2vw;
}

#creativity h2 {
    font-size: 1.2vw;
    margin-top: 2vh;
    font-weight: 400;
}

#communication {
    width: 40%;
    height: 40%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

#communication .progress {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40%;
    height: 70%;
    border-radius: 50%;
    background:
        radial-gradient(closest-side, rgb(30, 30, 43) 90%, transparent 80% 100%),
        conic-gradient(cyan 65%, rgb(0, 0, 0) 0);
}

#communication .progress::before {
    content: "65%";
    font-size: 1.2vw;
}

#communication h2 {
    font-size: 1.2vw;
    margin-top: 2vh;
    font-weight: 400;
}

#problem {
    width: 40%;
    height: 40%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

#problem .progress {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40%;
    height: 70%;
    border-radius: 50%;
    background:
        radial-gradient(closest-side, rgb(30, 30, 43) 90%, transparent 80% 100%),
        conic-gradient(cyan 60%, rgb(0, 0, 0) 0);
}

#problem .progress::before {
    content: "60%";
    font-size: 1.2vw;
}

#problem h2 {
    font-size: 1.2vw;
    margin-top: 2vh;
    font-weight: 400;
}

#team {
    width: 40%;
    height: 40%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

#team .progress {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40%;
    height: 70%;
    border-radius: 50%;
    background:
        radial-gradient(closest-side, rgb(30, 30, 43) 90%, transparent 80% 100%),
        conic-gradient(cyan 80%, rgb(0, 0, 0) 0);
}

#team .progress::before {
    content: "80%";
    font-size: 1.2vw;
}

#team h2 {
    font-size: 1.2vw;
    margin-top: 2vh;
    font-weight: 400;
}

.progress:hover {
    filter: drop-shadow(0px 0px 25px cyan);
}

#project {
    width: 100vw;
    min-height: 100vh;
    padding: 8vh 4vw;
}

#project h1 {
    text-align: center;
    font-size: 4vw;
}

#project h1:hover {
    color: transparent;
    -webkit-text-stroke: 1px white;
    filter: drop-shadow(0px 0px 10px cyan);
}

#items {
    width: 100%;
    height: 100%;
    margin: 8vh 0vw;
    /* border: 2px solid white; */
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#items .item {
    width: 30%;
    height: 25vw;
    /* border: 2px solid green; */
    border-radius: 10px;
    overflow: hidden;
}

#items .item:hover {
    box-shadow: 0px 0px 25px cyan;
}

#items .item img {
    width: 100%;
    height: 80%;
    object-fit: cover;
    object-position: center;
}

#items .item h2 {
    margin-top: 1vw;
    padding-left: 1vw;
    font-weight: 400;
    line-height: 1.3vw;
}

#contact {
    width: 100vw;
    max-height: 100vh;
    display: flex;
    padding: 2vh 2vw;
}

#lcontact {
    width: 50%;
    height: 100%;
    /* border: 2px solid white; */
    padding-top: 10vh;
}

#lcontact h1 {
    font-size: 2.5vw;
    text-align: center;
}

#lcontact h3 {
    font-size: 1.5vw;
    font-weight: 400;
    padding: 1vw 0vw;
}

#lcontact p {
    font-size: 1.2vw;
    color: #dadada;
    padding-bottom: 3vh;
}

#lcontact>i {
    font-size: 1.5vw;
    display: block;
    color: cyan;
    margin: 2vh 0vw;
}

#lcontact a {
    text-decoration: none;
}

#lcontact i span {
    color: white;
    font-size: 1vw;
}

#icon {
    margin-top: 3vw;
}

#icon i {
    border: 2px solid aqua;
    font-size: 1.2vw;
    border-radius: 50%;
    padding: 1.2vh;
    margin-right: 1vw;
    color: aqua;
}

#icon i:hover {
    box-shadow: 0px 0px 1vw aqua;
}

#rcontact {
    width: 50%;
    height: 100%;
    padding: 8vh 2vw;
    /* border: 2px solid yellow; */
}

/* Enhanced Contact Form */
.contact-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 400;
    transition: var(--transition-smooth);
    outline: none;
    font-family: inherit;
}

.contact-form input:focus,
.contact-form textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.contact-form textarea {
    min-height: 120px;
    resize: vertical;
}

::placeholder {
    color: var(--text-secondary);
    opacity: 0.8;
}

.submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: var(--gradient-accent);
    border: none;
    border-radius: var(--border-radius);
    color: var(--bg-primary);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 245, 255, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.form-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    opacity: 0;
    transition: var(--transition-smooth);
}

.form-status.success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid #4caf50;
    color: #4caf50;
    opacity: 1;
}

.form-status.error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid #f44336;
    color: #f44336;
    opacity: 1;
}

/* Enhanced Footer */
#footer {
    width: 100%;
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
    margin-top: 5rem;
}

.footer-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    color: var(--primary-color);
    opacity: 0.3;
}

.footer-wave svg {
    width: 100%;
    height: 100%;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3rem 5vw 2rem;
    position: relative;
    z-index: 1;
}

.footer-text h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.footer-text p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.heart {
    color: var(--secondary-color);
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-smooth);
    position: relative;
}

.footer-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition-smooth);
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-link:hover::after {
    width: 100%;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border: none;
    border-radius: 50%;
    color: var(--bg-primary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    box-shadow: 0 8px 25px rgba(0, 245, 255, 0.3);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 15px 35px rgba(0, 245, 255, 0.5);
}

/* Animation Classes for Intersection Observer */
.animate-in {
    animation: slideInUp 0.8s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading State */
body:not(.loaded) .service-card,
body:not(.loaded) .floating-icon {
    opacity: 0;
    transform: translateY(30px);
}

/* Smooth transitions for all interactive elements */
* {
    -webkit-tap-highlight-color: transparent;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-icon {
        animation: none;
    }

    .cursor-outline {
        animation: none;
    }
}

/* Modern Utility Classes */
.glassmorphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.text-gradient {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: var(--transition-smooth);
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    animation: slideInLeft 1s ease forwards;
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    animation: slideInRight 1s ease forwards;
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .name-title {
        font-size: 3rem;
    }

    .floating-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    :root {
        --border-radius: 12px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-primary,
    .btn-secondary {
        justify-content: center;
        width: 100%;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .floating-elements {
        display: none;
    }

    .image-container {
        width: 300px;
        height: 300px;
    }

    .profile-image {
        width: 280px;
        height: 280px;
    }

    .image-glow {
        width: 300px;
        height: 300px;
    }
}

@media (max-width:600px) {
    #nav-PopUp{
        display: block;
    }

    #navleft a {
        display: none;
    }

    #navleft i {
        display: block;
        font-size: 1.5rem;
        cursor: pointer;
    }

    #navright h3 {
        font-size: 3vh;
    }

    #home {
        flex-direction: column;
    }

    #homeright {
        width: 100vw;
        padding: 10vh 4vw;
        padding-bottom: 0vh;
        line-height: 5vh;
    }

    #homeright h2 {
        font-size: 3.5vh;
        line-height: 4vh;
        margin-top: 1vh;
        white-space: nowrap;
        margin-bottom: 1vh;
    }

    #homeright h2:nth-child(1) {
        font-size: 3.5vh;
        letter-spacing: 0.2vw;
        margin-bottom: 0.5vh;
    }

    #homeright h1 {
        letter-spacing: 0.2vw;
        font-size: 5vh;
    }

    #homeright p {
        font-size: 2.5vh;
        font-weight: 400;
        line-height: 4vh;
        padding-top: 3vw;
        padding-bottom: 3vh;
    }

    #homeright i {
        font-size: 3vh;
        margin-left: 1vh;
    }
    #homeright button {
        margin-top: 5vh;
        font-size: 3vh;
        font-weight: 600;
        border-radius: 1vh;
        padding: 2vh 2vw;
    }

    button {
        margin-top: 5vh;
        font-size: 2vh;
        border-radius: 1vh;
        padding: 2vh 2vw;
    }

    #homeleft {
        display: none;
    }

    #aboutme {
        flex-direction: column;
    }

    #aboutright {
        width: 100%;
    }

    #aboutleft {
        width: 100vw;
        /* border: 2px solid red; */
    }

    #aboutleft h1 {
        font-size: 5.5vh;
        font-weight: 400;
    }

    #aboutleft h2 {
        font-size: 2vh;
        letter-spacing: 0.2vh;
    }

    #aboutleft p {
        font-size: 1.9vh;
        padding-left: 1vh;
        line-height: 3vh;
    }

    #services {
        /* padding: 8vw 2.5vh; */
        width: 100vw;
        margin-top: 1vh;
        margin-bottom: 35vh;
    }

    #services h1 {
        padding-bottom: 5vw;
        font-size: 4vh;
    }

    #boxes {
        flex-direction: column;
    }

    .box {
        width: 100%;
        height: fit-content;
        /* border: 2px solid goldenrod; */
        padding: 2vh;
    }

    .box i {
        font-size: 2.5vh;
    }

    .box h3 {
        font-size: 1.9vh;
    }

    #skills {
        width: 100vw;
        padding: 0vh 3vw;
        margin-top: 15vh;
        /* border: 2px solid red; */
    }

    #skills>h1 {
        font-size: 3.5vh;
    }

    #skillchart {
        margin-top: 4vw;
        flex-direction: column;
    }

    .skill h1 {
        font-size: 2vh;
        text-align: center;
        margin-bottom: 0vh;
    }

    #skillright {
        width: 100%;
        /* border: 2px solid green; */
    }

    .percentage {
        font-size: 1.2vh;
    }

    #skillright i {
        font-size: 3vh;
    }

    #skillright h3 {
        font-size: 1.23vh;
    }

    #skillleft {
        width: 100%;
        padding: 1vh 2vw;
    }

    #contain {
        width: 100%;
        height: 50vh;
        /* border: 2px solid green; */
    }

    #creativity {
        width: 50%;
        height: 40%;
    }

    #creativity .progress {
        width: 50%;
        height: 60%;
    }

    #creativity .progress::before {
        font-size: 1.2vh;
    }

    #creativity h2 {
        font-size: 1.2vh;
        margin-top: 2vw;
    }

    #communication {
        width: 50%;
        height: 40%;
    }

    #communication .progress {
        width: 50%;
        height: 60%;
    }

    #communication .progress::before {
        font-size: 1.2vh;
    }

    #communication h2 {
        font-size: 1.2vh;
        margin-top: 2vw;
    }

    #problem {
        width: 50%;
        height: 40%;
    }

    #problem .progress {
        width: 50%;
        height: 60%;
    }

    #problem .progress::before {
        font-size: 1.2vh;
    }

    #problem h2 {
        font-size: 1.2vh;
        margin-top: 2vw;
    }

    #team {
        width: 50%;
        height: 40%;
    }

    #team .progress {
        width: 50%;
        height: 60%;
    }

    #team .progress::before {
        font-size: 1.2vh;
    }

    #team h2 {
        font-size: 1.2vh;
        margin-top: 2vw;
    }

    #project {
        /* border: 2px solid white; */
        width: 100vw;
        padding: 8vw 4vh;
    }

    #project h1 {
        font-size: 4vh;
    }

    #items {
        margin: 8vw 0vw;
        margin-bottom: 0vw;
        flex-direction: column;
    }

    #items .item {
        width: 100%;
        height: 25vh;
        margin-bottom: 2vw;
    }

    #items .item h2 {
        margin-top: 1vh;
        text-align: center;
        font-size: 3vw;
    }


    #contact {
        width: 100vw;
        flex-direction: column;
    }

    #lcontact {
        width: 100%;
        padding-top: 2vw;
    }

    #lcontact h1 {
        font-size: 4.5vh;
    }

    #lcontact h3 {
        font-size: 2.5vh;
    }

    #lcontact p {
        font-size: 1.8vh;
        line-height: 2.5vh;
        padding-bottom: 3vw;
    }

    #lcontact>i {
        font-size: 2vh;
        margin-bottom: 1.5vw;
        line-height: 1vh;
    }

    #lcontact i span {
        font-size: 2vh;
    }

    #icon {
        margin-top: 3.5vh;
    }

    #icon i {
        font-size: 2vh;
    }

    #rcontact {
        width: 100%;
        padding: 8vw 2vw;
    }

    form input {
        color:white;
        height: 4vh;
        font-size: 2vh;
        outline: none;
        padding: 2vh 2vw;
    }

    textarea {
        color:white;
        height: 8vh;
        font-size: 2vh;
        outline: none;
        padding: 2vw 2vw;
    }

    form button {
        font-size: 2.5vh;
        font-weight: 800;
    }

    .footer-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
        padding: 2rem 4vw;
    }

    .footer-text h2 {
        font-size: 1.2rem;
    }

    .footer-text p {
        font-size: 0.9rem;
    }

    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    .footer-link {
        font-size: 0.9rem;
    }

    #mousefollower {
        display: none;
    }
}