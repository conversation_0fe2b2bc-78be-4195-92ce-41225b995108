<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yashaswi Rai - Full Stack Developer</title>
    <meta name="description" content="Yashaswi Rai - Full Stack Web Developer specializing in React, Node.js, and modern web technologies">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@3.5.4/dist/locomotive-scroll.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Enhanced Loader -->
    <div id="loader">
        <div class="loader-content">
            <div class="loader-circle">
                <div class="loader-inner"></div>
            </div>
            <h1 class="loader-text">0%</h1>
            <p class="loader-subtitle">Loading Experience...</p>
        </div>
    </div>

    <!-- Enhanced Mouse Follower -->
    <div id="mousefollower">
        <div class="cursor-dot"></div>
        <div class="cursor-outline"></div>
    </div>

    <!-- Scroll Progress Indicator -->
    <div id="scroll-progress">
        <div class="progress-bar"></div>
    </div>

    <!-- Navigation -->
    <div id="nav">
        <div id="navright">
            <a href="#home">
                <h3>Portfolio.</h3>
            </a>
        </div>
        <div id="navleft">
            <a href="#home">Home</a>
            <a href="#aboutme">About</a>
            <a href="#services">Services</a>
            <a href="#skills">Skills</a>
            <a href="#project">Project</a>
            <a href="#contact">Contact</a>
            <i id="menu" class="ri-menu-fill"></i>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top" aria-label="Back to top">
        <i class="ri-arrow-up-line"></i>
    </button>
    <div id="main">
        <div id="nav-PopUp">
            <div id="links">
                <a href="#home">Home</a>
                <a href="#aboutme">About</a>
                <a href="#services">Services</a>
                <a href="#skills">Skills</a>
                <a href="#project">Project</a>
                <a href="#contact">Contact</a>
            </div>
            <i id="close" class="ri-close-line"></i>
        </div>

        <div id="home">
            <div id="homeright">
                <div class="hero-content">
                    <h2 class="greeting">Hello, It's me</h2>
                    <h1 class="name-title">
                        <span class="typing-text" data-text="Yashaswi Rai"></span>
                        <span class="cursor-blink">|</span>
                    </h1>
                    <h2 class="role-title">And I'm a <span class="highlight-text">Full Stack Developer</span></h2>
                    <p class="hero-description">I am a passionate Full Stack web developer with expertise in modern technologies like React, Node.js, Express, and MongoDB. I love creating engaging, responsive, and user-friendly web experiences that bring ideas to life.</p>

                    <div id="icons" class="social-icons">
                        <a href="https://www.facebook.com/yash.rahul.19" target="_blank" class="social-link" data-tooltip="Facebook">
                            <i class="ri-facebook-line"></i>
                        </a>
                        <a href="https://wa.me/+919062950674" target="_blank" class="social-link" data-tooltip="WhatsApp">
                            <i class="ri-whatsapp-line"></i>
                        </a>
                        <a href="https://www.instagram.com/_rahul_yash_/" target="_blank" class="social-link" data-tooltip="Instagram">
                            <i class="ri-instagram-line"></i>
                        </a>
                        <a href="https://www.linkedin.com/in/yashaswirai2003/" target="_blank" class="social-link" data-tooltip="LinkedIn">
                            <i class="ri-linkedin-line"></i>
                        </a>
                    </div>

                    <div class="hero-buttons">
                        <a href="https://www.linkedin.com/in/yashaswirai2003/" class="btn-primary magnetic-btn">
                            <span>More About Me</span>
                            <i class="ri-arrow-right-line"></i>
                        </a>
                        <a href="#contact" class="btn-secondary magnetic-btn">
                            <span>Let's Talk</span>
                            <i class="ri-chat-3-line"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div id="homeleft" class="image">
                <div class="image-container">
                    <div class="floating-elements">
                        <div class="floating-icon" data-icon="html"><i class="ri-html5-line"></i></div>
                        <div class="floating-icon" data-icon="css"><i class="ri-css3-line"></i></div>
                        <div class="floating-icon" data-icon="js"><i class="ri-javascript-line"></i></div>
                        <div class="floating-icon" data-icon="react"><i class="ri-reactjs-line"></i></div>
                    </div>
                    <img src="./mine.webp" alt="Yashaswi Rai - Full Stack Developer" class="profile-image">
                    <div class="image-glow"></div>
                </div>
            </div>
        </div>
        <div id="aboutme">
            <div id="aboutright">
                <img src="./mine.webp" alt="">
            </div>
            <div id="aboutleft">
                <h1>About <span>Me</span></h1>
                <h2> <span>Full Stack Developer</span></h2>
                <p>I am a skilled <span>Full Stack Developer</span>. I thrive on converting design concepts into
                    responsive, user-friendly websites while adhering to industry standards for accessibility,
                    performance, and security. Collaborative by nature, I enjoy working with cross-functional teams to
                    deliver seamless digital solutions. My commitment to ongoing learning ensures I stay current with
                    the latest web development trends, making me a valuable asset for any project..</p>
                <a href="https://www.linkedin.com/in/yashaswirai2003/" target="_blank"><Button>More About
                        ME</Button></a>
            </div>
        </div>



        <div id="services">
            <div class="section-header">
                <h1>My <span class="highlight-text">Services</span></h1>
                <p class="section-subtitle">What I offer to bring your ideas to life</p>
            </div>
            <div id="boxes" class="services-grid">
                <div class="service-card" data-tilt>
                    <div class="card-header">
                        <div class="icon-wrapper">
                            <i class="ri-server-line"></i>
                        </div>
                        <h3>Backend Development</h3>
                    </div>
                    <div class="card-content">
                        <p>Robust server-side solutions with Node.js, Express, and database management. I build scalable APIs, handle authentication, and ensure optimal performance and security for your applications.</p>
                        <ul class="service-features">
                            <li><i class="ri-check-line"></i> RESTful API Development</li>
                            <li><i class="ri-check-line"></i> Database Design & Management</li>
                            <li><i class="ri-check-line"></i> Authentication & Security</li>
                        </ul>
                    </div>
                    <div class="card-footer">
                        <a href="https://www.geeksforgeeks.org/backend-development/" target="_blank" class="service-btn magnetic-btn">
                            <span>Learn More</span>
                            <i class="ri-external-link-line"></i>
                        </a>
                    </div>
                </div>

                <div class="service-card" data-tilt>
                    <div class="card-header">
                        <div class="icon-wrapper">
                            <i class="ri-code-s-slash-line"></i>
                        </div>
                        <h3>Full Stack Development</h3>
                    </div>
                    <div class="card-content">
                        <p>Complete web solutions from concept to deployment. I handle both frontend and backend development, ensuring seamless integration and optimal user experience across all devices.</p>
                        <ul class="service-features">
                            <li><i class="ri-check-line"></i> End-to-End Development</li>
                            <li><i class="ri-check-line"></i> Modern Tech Stack</li>
                            <li><i class="ri-check-line"></i> Responsive Design</li>
                        </ul>
                    </div>
                    <div class="card-footer">
                        <a href="https://www.geeksforgeeks.org/what-is-software-development/" target="_blank" class="service-btn magnetic-btn">
                            <span>Learn More</span>
                            <i class="ri-external-link-line"></i>
                        </a>
                    </div>
                </div>

                <div class="service-card" data-tilt>
                    <div class="card-header">
                        <div class="icon-wrapper">
                            <i class="ri-palette-line"></i>
                        </div>
                        <h3>Frontend Development</h3>
                    </div>
                    <div class="card-content">
                        <p>Beautiful, interactive user interfaces using React, modern CSS, and JavaScript. I create engaging experiences that are both visually appealing and highly functional.</p>
                        <ul class="service-features">
                            <li><i class="ri-check-line"></i> React & Modern Frameworks</li>
                            <li><i class="ri-check-line"></i> Interactive UI/UX</li>
                            <li><i class="ri-check-line"></i> Performance Optimization</li>
                        </ul>
                    </div>
                    <div class="card-footer">
                        <a href="https://www.w3schools.com/whatis/whatis_frontenddev.asp" target="_blank" class="service-btn magnetic-btn">
                            <span>Learn More</span>
                            <i class="ri-external-link-line"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div id="skills">
            <h1>My <span>Skills</span></h1>
            <div id="skillchart">
                <div id="skillright" class="skill">
                    <h1>Technical Skills</h1>
                    <div id="html">
                        <i class="ri-html5-line"></i>
                        <div class="percentage">
                            <h3 class="head">HTML</h3>
                            <h5>90%</h5>
                        </div>
                        <div class="bar">
                            <div id="hpercent" class="anim"></div>
                        </div>
                    </div>
                    <div id="css">
                        <i class="ri-css3-fill"></i>
                        <div class="percentage">
                            <h3 class="head">CSS</h3>
                            <h5>85%</h5>
                        </div>
                        <div class="bar">
                            <div id="cpercent" class="anim"></div>
                        </div>
                    </div>
                    <div id="js">
                        <i class="ri-javascript-fill"></i>
                        <div class="percentage">
                            <h3 class="head">Javascript</h3>
                            <h5>85%</h5>
                        </div>
                        <div class="bar">
                            <div id="jspercent" class="anim"></div>
                        </div>
                    </div>
                    <div id="react">
                        <i class="ri-reactjs-line"></i>
                        <div class="percentage">
                            <h3 class="head">React</h3>
                            <h5>75%</h5>
                        </div>
                        <div class="bar">
                            <div id="reactpercent" class="anim"></div>
                        </div>
                    </div>
                </div>
                <div id="skillleft" class="skill">
                    <h1>Professional Skills</h1>
                    <div id="contain">
                        <div id="creativity">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Creativity</h2>
                        </div>
                        <div id="communication">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Communication</h2>
                        </div>
                        <div id="problem">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Problem Solving</h2>
                        </div>
                        <div id="team">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Teamwork</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="project">
            <h1>Latest <span>Project</span></h1>
            <div id="items">
                <div class="item">
                    <a href="https://yashaswirai.github.io/CubertoClone/"><img src="https://cdn.cuberto.com/cb/upload/885fbbc555395f745746b23b73f539f5.png"></a>
                    <h2>Clonning awarded website cuberto.com</h2>
                </div>
                <div class="item">
                    <a href="https://yashaswirai.github.io/web_project1/" target="_blank"><img
                            src="https://assets.awwwards.com/awards/element/2023/05/646866fdcb69a649837017.png"></a>
                    <h2>Clonning awarded website cynthiaugwu.com</h2>
                </div>
                <div class="item">
                    <a href="https://yashaswi-ecommerce-react.vercel.app/"><img
                            src="https://www.crio.do/blog/content/images/2021/03/Javascript-projects--React.png"></a>
                    <h2>An Ecommerce project using React</h2>
                </div>
            </div>
        </div>
        <div id="contact">
            <div id="lcontact">
                <h1>Contact <span>Me</span></h1>
                <h3>Let's Work Together</h3>
                <p>Let's work together to transform ideas into digital reality, leveraging my web development skills and
                    your vision to create outstanding online experiences that captivate and engage audiences</p>
                <i class="ri-mail-send-fill"><span> <EMAIL></span></i>
                <i class="ri-customer-service-2-fill"><span> +91 9062950674</span></i>
                <div id="icon">
                    <a href="https://www.facebook.com/yash.rahul.19" target="_blank"><i
                            class="ri-facebook-line"></i></a>
                    <a href="https:wa.me/+919062950674" target="_blank"><i class="ri-whatsapp-line"></i></a>
                    <a href="https://www.instagram.com/_rahul_yash_/" target="_blank"><i
                            class="ri-instagram-line"></i></a>
                    <a href="https://www.linkedin.com/in/yashaswirai2003/" target="_blank"><i
                            class="ri-linkedin-line"></i></a>
                </div>
            </div>
            <div id="rcontact">
                <form action="#" class="contact-form" id="contactForm">
                    <input type="text" id="name" name="name" placeholder="Enter your Name" required>
                    <input type="email" id="email" name="email" placeholder="Enter your E-mail" required>
                    <input type="text" id="subject" name="subject" placeholder="Enter your subject" required>
                    <textarea name="message" id="message" cols="30" rows="5" placeholder="Enter the message" required></textarea>
                    <button type="submit" class="submit-btn magnetic-btn">
                        <span>Send Message</span>
                        <i class="ri-send-plane-line"></i>
                    </button>
                    <div class="form-status" id="formStatus"></div>
                </form>
            </div>
        </div>
        <div id="footer">
            <div class="footer-content">
                <div class="footer-text">
                    <h2>Developed with <span class="heart">❤️</span> by <span class="highlight-text">Yashaswi Rai</span></h2>
                    <p>&copy; 2024 All rights reserved</p>
                </div>
                <div class="footer-links">
                    <a href="#home" class="footer-link">Home</a>
                    <a href="#aboutme" class="footer-link">About</a>
                    <a href="#services" class="footer-link">Services</a>
                    <a href="#contact" class="footer-link">Contact</a>
                </div>
            </div>
            <div class="footer-wave">
                <svg viewBox="0 0 1200 120" preserveAspectRatio="none">
                    <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="currentColor"></path>
                    <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="currentColor"></path>
                    <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="currentColor"></path>
                </svg>
            </div>
        </div>
    </div>
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@3.5.4/dist/locomotive-scroll.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"
        integrity="sha512-16esztaSRplJROstbIIdwX3N97V1+pZvV33ABoG1H2OyTttBxEGkTsoIVsiP1iaTtM8b3+hu2kB6pQ4Clr5yug=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"
        integrity="sha512-Ic9xkERjyZ1xgJ5svx3y0u3xrvfT/uPkV99LBwe68xjy/mGtO+4eURHZBW2xW4SZbFrF1Tf090XqB+EVgXnVjw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vanilla-tilt/1.8.0/vanilla-tilt.min.js"></script>
    <script src="script.js"></script>
</body>

</html>