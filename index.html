<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yashaswi Rai</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@3.5.4/dist/locomotive-scroll.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div id="loader">
        <h1>0%</h1>
    </div>
    <div id="mousefollower"></div>
    <div id="nav">
        <div id="navright">
            <a href="/">
                <h3>Portfolio.</h3>
            </a>
        </div>
        <div id="navleft">
            <a href="#home">Home</a>
            <a href="#aboutme">About</a>
            <a href="#services">Services</a>
            <a href="#skills">Skills</a>
            <a href="#project">Project</a>
            <a href="#contact">Contact</a>
            <i id="menu" class="ri-menu-fill"></i>
        </div>
    </div>
    <div id="main">
        <div id="nav-PopUp">
            <div id="links">
                <a href="#home">Home</a>
                <a href="#aboutme">About</a>
                <a href="#services">Services</a>
                <a href="#skills">Skills</a>
                <a href="#project">Project</a>
                <a href="#contact">Contact</a>
            </div>
            <i id="close" class="ri-close-line"></i>
        </div>

        <div id="home">
            <div id="homeright">
                <h2>Hello, It's me</h2>
                <h1>Yashaswi Rai</h1>
                <h2>And I'm a <span>Web Developer</span></h2>
                <p>I am an intermediate-level Full Stack web developer with proficiency in HTML, CSS, JavaScript, and
                    frameworks like React while, in the backend I have good foundation of knowledge in Node.js, EXPRESS
                    and MongoDB. My passion for creating engaging online experiences drives my work.</p>
                <div id="icons">
                    <a href="https://www.facebook.com/yash.rahul.19" target="_blank"><i
                            class="ri-facebook-line"></i></a>
                    <a href="https:wa.me/+919062950674" target="_blank"><i class="ri-whatsapp-line"></i></a>
                    <a href="https://www.instagram.com/_rahul_yash_/" target="_blank"><i
                            class="ri-instagram-line"></i></a>
                    <a href="https://www.linkedin.com/in/yashaswirai2003/" target="_blank"><i
                            class="ri-linkedin-line"></i></a>
                </div>
                <a href="https://www.linkedin.com/in/yashaswirai2003/"><button>More About Me</button></a>
            </div>
            <div id="homeleft" class="image">
                <img src="./mine.webp" alt="">
            </div>
        </div>
        <div id="aboutme">
            <div id="aboutright">
                <img src="./mine.webp" alt="">
            </div>
            <div id="aboutleft">
                <h1>About <span>Me</span></h1>
                <h2> <span>Full Stack Developer</span></h2>
                <p>I am a skilled <span>Full Stack Developer</span>. I thrive on converting design concepts into
                    responsive, user-friendly websites while adhering to industry standards for accessibility,
                    performance, and security. Collaborative by nature, I enjoy working with cross-functional teams to
                    deliver seamless digital solutions. My commitment to ongoing learning ensures I stay current with
                    the latest web development trends, making me a valuable asset for any project..</p>
                <a href="https://www.linkedin.com/in/yashaswirai2003/" target="_blank"><Button>More About
                        ME</Button></a>
            </div>
        </div>



        <div id="services">
            <h1>My <span>Services</span></h1>
            <div id="boxes">
                <div class="box">
                    <i class="ri-shield-fill"></i>
                    <h3>Backend Development</h3>
                    <p>Backend development refers to the server-side of a website or web application. It is the part of
                        the application that is not visible to the user, but it is essential for the website or web
                        application to function properly. Backend development includes server-side programming, database
                        management, and security.</p>
                    <a href="https://www.geeksforgeeks.org/backend-development/" target="_blank"><button>Learn
                            More</button></a>
                </div>
                <div class="box">
                    <i class="ri-code-s-slash-line"></i>
                    <h3>Software Development</h3>
                    <p>Software development is the process of creating software applications. It involves designing, coding, testing, and maintaining software applications. Software development is a complex process that requires a team of developers, designers, and testers to work together to create a successful software application.</p>
                    <a href="https://www.geeksforgeeks.org/what-is-software-development/"
                        target="_blank"></a><button>Learn More</button>
                </div>
                <div class="box">
                    <i class="ri-command-line"></i>
                    <h3>Frontened Developer</h3>
                    <p> front-end developer builds the front-end portion of websites and web applications—the part users
                        see and interact with. A front-end developer creates websites and applications using web
                        languages such as HTML, CSS, and JavaScript that allow users to access and interact with the
                        site or app.</p>
                    <a href="https://www.w3schools.com/whatis/whatis_frontenddev.asp" target="_blank"></a><button>Learn
                        More</button>
                </div>
            </div>
        </div>
        <div id="skills">
            <h1>My <span>Skills</span></h1>
            <div id="skillchart">
                <div id="skillright" class="skill">
                    <h1>Technical Skills</h1>
                    <div id="html">
                        <i class="ri-html5-line"></i>
                        <div class="percentage">
                            <h3 class="head">HTML</h3>
                            <h5>90%</h5>
                        </div>
                        <div class="bar">
                            <div id="hpercent" class="anim"></div>
                        </div>
                    </div>
                    <div id="css">
                        <i class="ri-css3-fill"></i>
                        <div class="percentage">
                            <h3 class="head">CSS</h3>
                            <h5>85%</h5>
                        </div>
                        <div class="bar">
                            <div id="cpercent" class="anim"></div>
                        </div>
                    </div>
                    <div id="js">
                        <i class="ri-javascript-fill"></i>
                        <div class="percentage">
                            <h3 class="head">Javascript</h3>
                            <h5>85%</h5>
                        </div>
                        <div class="bar">
                            <div id="jspercent" class="anim"></div>
                        </div>
                    </div>
                    <div id="react">
                        <i class="ri-reactjs-line"></i>
                        <div class="percentage">
                            <h3 class="head">React</h3>
                            <h5>75%</h5>
                        </div>
                        <div class="bar">
                            <div id="reactpercent" class="anim"></div>
                        </div>
                    </div>
                </div>
                <div id="skillleft" class="skill">
                    <h1>Professional Skills</h1>
                    <div id="contain">
                        <div id="creativity">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Creativity</h2>
                        </div>
                        <div id="communication">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Communication</h2>
                        </div>
                        <div id="problem">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Problem Solving</h2>
                        </div>
                        <div id="team">
                            <div class="progress">
                                <progress value="75" min="0" max="100" style="visibility:hidden;height:0;width:0"
                                    ;></progress>
                            </div>
                            <h2>Teamwork</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="project">
            <h1>Latest <span>Project</span></h1>
            <div id="items">
                <div class="item">
                    <a href="https://yashaswirai.github.io/CubertoClone/"><img src="https://cdn.cuberto.com/cb/upload/885fbbc555395f745746b23b73f539f5.png"></a>
                    <h2>Clonning awarded website cuberto.com</h2>
                </div>
                <div class="item">
                    <a href="https://yashaswirai.github.io/web_project1/" target="_blank"><img
                            src="https://assets.awwwards.com/awards/element/2023/05/646866fdcb69a649837017.png"></a>
                    <h2>Clonning awarded website cynthiaugwu.com</h2>
                </div>
                <div class="item">
                    <a href="https://yashaswi-ecommerce-react.vercel.app/"><img
                            src="https://www.crio.do/blog/content/images/2021/03/Javascript-projects--React.png"></a>
                    <h2>An Ecommerce project using React</h2>
                </div>
            </div>
        </div>
        <div id="contact">
            <div id="lcontact">
                <h1>Contact <span>Me</span></h1>
                <h3>Let's Work Together</h3>
                <p>Let's work together to transform ideas into digital reality, leveraging my web development skills and
                    your vision to create outstanding online experiences that captivate and engage audiences</p>
                <i class="ri-mail-send-fill"><span> <EMAIL></span></i>
                <i class="ri-customer-service-2-fill"><span> +91 9062950674</span></i>
                <div id="icon">
                    <a href="https://www.facebook.com/yash.rahul.19" target="_blank"><i
                            class="ri-facebook-line"></i></a>
                    <a href="https:wa.me/+919062950674" target="_blank"><i class="ri-whatsapp-line"></i></a>
                    <a href="https://www.instagram.com/_rahul_yash_/" target="_blank"><i
                            class="ri-instagram-line"></i></a>
                    <a href="https://www.linkedin.com/in/yashaswirai2003/" target="_blank"><i
                            class="ri-linkedin-line"></i></a>
                </div>
            </div>
            <div id="rcontact">
                <form action="#">
                    <input type="text" placeholder="Enter your Name">
                    <input type="email" placeholder="Enter your E-mail">
                    <input type="text" placeholder="Enter your subject">
                    <textarea name="Message" cols="30" rows="5" placeholder="Enter the message"></textarea>
                    <button type="submit">Submit</button>
                </form>
            </div>
        </div>
        <div id="footer">
            <h2>Developed with love by Yashaswi Rai &#169; 2023</h2>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@3.5.4/dist/locomotive-scroll.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"
        integrity="sha512-16esztaSRplJROstbIIdwX3N97V1+pZvV33ABoG1H2OyTttBxEGkTsoIVsiP1iaTtM8b3+hu2kB6pQ4Clr5yug=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"
        integrity="sha512-Ic9xkERjyZ1xgJ5svx3y0u3xrvfT/uPkV99LBwe68xjy/mGtO+4eURHZBW2xW4SZbFrF1Tf090XqB+EVgXnVjw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="script.js"></script>
</body>

</html>